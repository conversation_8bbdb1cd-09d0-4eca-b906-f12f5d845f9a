<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class Auth implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        // Get the current URI path
        $currentPath = $request->uri->getPath();

        // Clean up the path to handle potential index.php and public folder for XAMPP
        $currentPath = preg_replace('#^(public/)?(index\.php/)?#', '', $currentPath);
        $currentPath = trim($currentPath, '/');

        // Log the current path for debugging
        log_message('debug', 'Auth Filter: Processing path: ' . $currentPath);

        if (!empty($arguments)) {
            log_message('debug', 'Auth Filter: Arguments: ' . print_r($arguments, true));
        }

        // Check if route is publicly accessible using accept function
        if ($this->acceptRoute($currentPath)) {
            log_message('debug', 'Auth Filter: Allowing public route: ' . $currentPath);
            return;
        }

        // Check if any arguments were passed for role-based access
        if (!empty($arguments)) {
            // Check for applicant authentication
            if (in_array('applicant', $arguments)) {
                log_message('debug', 'Auth Filter: Checking applicant login for: ' . $currentPath);

                if (!session()->get('applicant_id')) {
                    log_message('debug', 'Auth Filter: No applicant_id in session, redirecting to login');
                    return $this->redirectToLogin($request, 'Please login to continue');
                }

                log_message('debug', 'Auth Filter: Applicant is logged in, allowing access');
                return;
            }

            // Check for admin role-based access
            if (!session()->get('logged_in') || session()->get('role') !== $arguments[0]) {
                log_message('debug', 'Auth Filter: Access denied, role mismatch');
                return $this->redirectToLogin($request, 'Access denied. Login required.');
            }

            log_message('debug', 'Auth Filter: Admin has correct role, allowing access');
            return;
        }

        // Default admin authentication check
        if (!session()->get('logged_in')) {
            log_message('debug', 'Auth Filter: No admin login, redirecting to home login page');
            return $this->redirectToLogin($request, 'Login required to access this page.');
        }

        log_message('debug', 'Auth Filter: Admin is logged in, allowing access');
    }

    /**
     * Accept function to determine if a route is publicly accessible without authentication
     *
     * @param string $path The current URI path
     * @return bool True if route is publicly accessible, false if authentication required
     */
    private function acceptRoute(string $path): bool
    {
        // Empty path (home page)
        if (empty($path) || $path === '/') {
            return true;
        }

        // Public routes that don't require authentication
        $publicRoutes = [
            'login',
            'logout',
            'about',
            'assets',
            'public',
            'jobs',
            'dakoii',
            'dakoii/login',
            'applicant/jobs',
            'applicant/register',
            'applicant/activate',
            'applicant/login',
            'applicant/logout',
        ];

        // Check for exact matches
        if (in_array($path, $publicRoutes)) {
            return true;
        }

        // Check for prefix matches
        foreach ($publicRoutes as $route) {
            if (strpos($path, $route . '/') === 0) {
                return true;
            }
        }

        // Special patterns that are publicly accessible
        $publicPatterns = [
            '#^jobs/view/\d+$#',                    // Job view pages
            '#^view_letter/#',                      // Letter view routes
            '#^process_letter/#',                   // Letter process routes
            '#^assets/#',                           // Asset files
            '#^public/#',                           // Public files
        ];

        foreach ($publicPatterns as $pattern) {
            if (preg_match($pattern, $path)) {
                return true;
            }
        }

        // All other routes require authentication
        return false;
    }

    /**
     * Redirect to login page with appropriate error message
     *
     * @param RequestInterface $request
     * @param string $message Error message to display
     * @return mixed
     */
    private function redirectToLogin(RequestInterface $request, string $message = 'Login required')
    {
        // Handle AJAX requests
        if ($request->header('X-Requested-With') === 'XMLHttpRequest') {
            log_message('debug', 'Auth Filter: AJAX request without authentication, returning 401');

            return service('response')
                ->setJSON([
                    'success' => false,
                    'message' => $message,
                    'redirect' => base_url('login')
                ])
                ->setStatusCode(401);
        }

        // For normal requests, redirect to home login page
        log_message('debug', 'Auth Filter: Redirecting to home login page with message: ' . $message);

        return redirect()
            ->to(base_url('login'))
            ->with('error', $message);
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // No after-filter needed
    }
}
