<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class Auth implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        // Get the current URI path
        $currentPath = $request->uri->getPath();

        // Clean up the path to handle potential index.php and public folder
        $currentPath = preg_replace('#^(public/)?(index\.php/)?#', '', $currentPath);

        // Log the current path for debugging
        log_message('debug', 'Auth Filter: Processing path: ' . $currentPath);

        if (!empty($arguments)) {
            log_message('debug', 'Auth Filter: Arguments: ' . print_r($arguments, true));
        }

        // These routes are always allowed without authentication
        $publicRoutes = [
            'dakoii',
            'dakoii/login',
            'login',
            'logout',
            'applicant/jobs',
            'applicant/jobs/position',
            'applicant/register',
            'applicant/activate',
            'applicant/login',
            'applicant/logout',
            '/',
            'about',
            'assets',
            'public',
            'jobs',
            'jobs/view',
            // Add other public routes as needed
        ];

        // Check for exact matches first
        if (in_array($currentPath, $publicRoutes)) {
            log_message('debug', 'Auth Filter: Allowing public route (exact match): ' . $currentPath);
            return;
        }

        // Then check for prefix matches
        foreach ($publicRoutes as $route) {
            if (strpos($currentPath, $route.'/') === 0) {
                log_message('debug', 'Auth Filter: Allowing public route (prefix match): ' . $currentPath . ' matches prefix ' . $route);
                return;
            }
        }

        // Special case for jobs/view/ID routes
        if (preg_match('#^jobs/view/\d+$#', $currentPath)) {
            log_message('debug', 'Auth Filter: Allowing jobs view route: ' . $currentPath);
            return;
        }

        // Allow public access to letter view and process routes
        if (preg_match('#^view_letter/|^process_letter/#', $currentPath)) {
            log_message('debug', 'Auth Filter: Allowing letter route: ' . $currentPath);
            return;
        }

        // Check if any arguments were passed
        if (!empty($arguments)) {
            // Check for applicant authentication
            if (in_array('applicant', $arguments)) {
                log_message('debug', 'Auth Filter: Checking applicant login for: ' . $currentPath);

                if (!session()->get('applicant_id')) {
                    log_message('debug', 'Auth Filter: No applicant_id in session, redirecting');

                    // Handle AJAX requests
                    if ($request->header('X-Requested-With') === 'XMLHttpRequest') {
                        return service('response')
                            ->setJSON([
                                'success' => false,
                                'message' => 'Please login to continue',
                                'redirect' => base_url('/')
                            ])
                            ->setStatusCode(401);
                    }

                    // For normal requests
                    return redirect()
                        ->to('/')
                        ->with('error', 'Please login to continue');
                }

                log_message('debug', 'Auth Filter: Applicant is logged in, allowing access');
                return;
            }

            // Check for admin role-based access
            if (!session()->get('logged_in') || session()->get('role') !== $arguments[0]) {
                log_message('debug', 'Auth Filter: Access denied, role mismatch');
                return redirect()->back()->with('error', 'Access denied');
            }

            log_message('debug', 'Auth Filter: Admin has correct role, allowing access');
            return;
        }

        // Default admin authentication check
        if (!session()->get('logged_in')) {
            log_message('debug', 'Auth Filter: No admin login, redirecting');

            // If trying to access Dakoii protected routes (but not login)
            if (strpos($currentPath, 'dakoii/') === 0 && !in_array($currentPath, $publicRoutes)) {
                return redirect()->to(base_url('dakoii'))->with('error', 'Please login to access Dakoii dashboard');
            }

            // Handle AJAX requests for pre-screening routes
            if (($request->header('X-Requested-With') === 'XMLHttpRequest') &&
                (strpos($currentPath, 'application_pre_screening/') === 0 ||
                 strpos($currentPath, 'applications_pre_screening') === 0)) {

                log_message('debug', 'Auth Filter: AJAX request for pre-screening with no session, returning 401');

                return service('response')
                    ->setJSON([
                        'success' => false,
                        'message' => 'Your session has expired. Please login again.',
                        'redirect' => base_url()
                    ])
                    ->setStatusCode(401);
            }

            return redirect()->to(base_url())->with('error', 'Please login to continue');
        }

        log_message('debug', 'Auth Filter: Admin is logged in, allowing access');
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // No after-filter needed
    }
}
