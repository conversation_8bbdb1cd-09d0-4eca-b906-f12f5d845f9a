<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;
use CodeIgniter\Filters\CSRF;
use CodeIgniter\Filters\DebugToolbar;
use CodeIgniter\Filters\Honeypot;
use CodeIgniter\Filters\InvalidChars;
use CodeIgniter\Filters\SecureHeaders;

class Filters extends BaseConfig
{
    /**
     * Configures aliases for Filter classes to
     * make reading things nicer and simpler.
     */
    public array $aliases = [
        'csrf'          => CSRF::class,
        'toolbar'       => DebugToolbar::class,
        'honeypot'      => Honeypot::class,
        'invalidchars'  => InvalidChars::class,
        'secureheaders' => SecureHeaders::class,
        'auth'          => \App\Filters\Auth::class,
    ];

    /**
     * List of filter aliases that are always
     * applied before and after every request.
     */
    public array $globals = [
        'before' => [
            'csrf' => ['except' => [
                'api/*',
                'applicant/login',
                'applicant/check-email',
                'profile_applications_exercise/generate_profile/*'
            ]]
        ],
        'after' => [
            'toolbar',
        ],
    ];

    /**
     * List of filter aliases that works on a
     * particular HTTP method (GET, POST, etc.).
     */
    public array $methods = [];

    /**
     * List of filter aliases that should run on any
     * before or after URI patterns.
     */
    public array $filters = [
        'auth' => [
            'before' => [
                'dashboard',
                'dashboard/*',
                'admin/dashboard',
                'admin/dashboard/*',
                'exercises',
                'exercises/*',
                'positions',
                'positions/*',
                'profile_applications_exercise',
                'profile_applications_exercise/*',
                'application_pre_screening',
                'application_pre_screening/*',
                'dakoii/dashboard',
                'dakoii/dashboard/*',
                'dakoii/organization',
                'dakoii/organization/*',
                'dakoii/system-user',
                'dakoii/system-user/*',
                'dakoii/province',
                'dakoii/province/*',
                'dakoii/district',
                'dakoii/district/*',
                'dakoii/education',
                'dakoii/education/*',
                'dakoii/rating_items',
                'dakoii/rating_items/*',
                'dakoii/rating_scores',
                'dakoii/rating_scores/*',
                'incoming_applications',
                'incoming_applications/*',
                'incoming_applicants',
                'incoming_applicants/*',
                'rating',
                'rating/*',
                'shortlisting',
                'shortlisting/*',
                'interviews',
                'interviews/*',
                'reports',
                'reports/*',
                'settings',
                'settings/*'
            ]
        ]
    ];
}
